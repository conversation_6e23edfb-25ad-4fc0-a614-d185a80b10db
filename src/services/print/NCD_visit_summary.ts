import { ConceptService } from "../concept_service";

export async function NCDVisitSummary(patientData: any) {
    console.log("🚀 ~ NCDVisitSummary ~ patientData:", patientData);
    // Get the latest visit date from visitsDates array
    const visitDates = patientData.visits.visitsDates.map((date: string) => new Date(date));
    const latestVisitDate = new Date(Math.max(...visitDates));

    // Format the date for display
    const formattedVisitDate = latestVisitDate.toISOString().split("T")[0];

    // Extract patient information
    const patientName = `${patientData.personInformation.given_name} ${patientData.personInformation.family_name}`;

    // Combine saved and unsaved arrays for all data types
    const allVitals = [...(patientData.vitals.saved || []), ...(patientData.vitals.unsaved || [])];

    const allDiagnoses = [...(patientData.diagnosis.saved || []), ...(patientData.diagnosis.unsaved || [])];

    const allMedications = [...(patientData.MedicationOrder.saved || []), ...(patientData.MedicationOrder.unsaved || [])];

    const allLabOrders = [...(patientData.labOrders.saved || []), ...(patientData.labOrders.unsaved || [])];

    // Extract vitals from the latest visit
    const vitals = allVitals.filter((vital: any) => new Date(vital.obs_datetime).toDateString() === latestVisitDate.toDateString());

    // Extract diagnoses from the latest visit
    const diagnoses = allDiagnoses.filter((diagnosis: any) => new Date(diagnosis.obs_datetime).toDateString() === latestVisitDate.toDateString());

    // Extract medications from the latest visit
    const medications = allMedications.filter((med: any) => new Date(med.encounter_date).toDateString() === latestVisitDate.toDateString());

    // Extract lab orders from the latest visit
    const labOrders = allLabOrders.filter((lab: any) => new Date(lab.order_date).toDateString() === latestVisitDate.toDateString());

    // Extract the prescriber (not directly available in the data)
    const prescriber = labOrders.length > 0 ? labOrders[0].requesting_clinician : undefined;

    // Next appointment information
    const nextAppointment = patientData.appointments.saved.length > 0 ? patientData.appointments.saved[0].value : undefined;

    // Resolve diagnosis concept names using Promise.all before building the summary
    const diagnosisNames = await Promise.all(diagnoses.map(async (d: any) => await ConceptService.getConceptName(d.value_coded)));

    // Helper function to safely extract vital values
    const getVitalValue = (conceptName: string, unit: string): string | undefined => {
        const vital = vitals.find((v: any) => v.concept_name === conceptName);
        return vital?.value_numeric !== undefined ? `${vital.value_numeric} ${unit}` : undefined;
    };

    // Compile the extracted information with undefined check
    const patientSummary = {
        patientID: patientData.NcdID,
        patientName: patientName,
        latestVisitDate: formattedVisitDate,
        vitals: {
            height: getVitalValue("Height (cm)", "cm"),
            weight: getVitalValue("Weight (kg)", "kg"),
            systolic: getVitalValue("Systolic blood pressure", ""),
            diastolic: getVitalValue("Diastolic blood pressure", ""),
            temperature: getVitalValue("Temperature (c)", "°C"),
            pulse: getVitalValue("Pulse", "bpm"),
            respRate: getVitalValue("Respiratory rate", "breaths/min"),
            o2Sat: getVitalValue("Blood oxygen saturation", "%"),
        },
        diagnoses: diagnosisNames.length > 0 ? diagnosisNames : undefined,
        medications:
            medications.length > 0
                ? medications.map((m: any) => ({
                      name: m.drug_name,
                      dose: m.dose ? `${m.dose} ${m.units || ""}`.trim() : undefined,
                      frequency: m.frequency,
                      duration:
                          m.expire_date && m.start_date
                              ? Math.ceil((Number(new Date(m.expire_date)) - Number(new Date(m.start_date))) / (1000 * 60 * 60 * 24))
                              : undefined,
                  }))
                : undefined,
        labOrders:
            labOrders.length > 0
                ? labOrders.map((l: any) => ({
                      test: l.tests && l.tests.length > 0 ? l.tests[0].name : undefined,
                      orderDate: l.order_date ? new Date(l.order_date).toISOString().split("T")[0] : undefined,
                      accessionNumber: l.accession_number,
                  }))
                : undefined,
        prescriber: prescriber,
        nextAppointment: nextAppointment,
    };

    // Helper function to create BP string only if both values exist
    const getBPString = (): string | undefined => {
        if (patientSummary.vitals.systolic && patientSummary.vitals.diastolic) {
            return `${patientSummary.vitals.systolic}/${patientSummary.vitals.diastolic} mmHg`;
        }
        return undefined;
    };

    // Helper function to build HTML for vitals with undefined checks
    const buildVitalsHTML = (): string => {
        const vitalParts = [];
        if (patientSummary.vitals.height) vitalParts.push(`Ht: ${patientSummary.vitals.height}`);
        if (patientSummary.vitals.weight) vitalParts.push(`Wt: ${patientSummary.vitals.weight}`);

        const bp = getBPString();
        if (bp) vitalParts.push(`BP: ${bp}`);

        if (patientSummary.vitals.temperature) vitalParts.push(`T: ${patientSummary.vitals.temperature}`);
        if (patientSummary.vitals.pulse) vitalParts.push(`P: ${patientSummary.vitals.pulse}`);
        if (patientSummary.vitals.respRate) vitalParts.push(`RR: ${patientSummary.vitals.respRate}`);
        if (patientSummary.vitals.o2Sat) vitalParts.push(`O₂: ${patientSummary.vitals.o2Sat}`);

        return vitalParts.length > 0 ? vitalParts.join(", ") : "";
    };

    // Helper function to build medication HTML with undefined checks
    const buildMedicationsHTML = (): string => {
        if (!patientSummary.medications) return "";

        return patientSummary.medications
            .map((med: any) => {
                const parts = [med.name];

                if (med.dose) parts.push(med.dose);
                if (med.frequency) parts.push(med.frequency);
                if (med.duration !== undefined) parts.push(`Duration: ${med.duration} day(s)`);

                return parts.join(", ");
            })
            .join("; ");
    };

    // Helper function to build lab orders HTML with undefined checks
    const buildLabsHTML = (): string => {
        if (!patientSummary.labOrders) return "";

        return patientSummary.labOrders
            .map((lab: any) => {
                const parts = [];

                if (lab.test) parts.push(lab.test);
                if (lab.orderDate) parts.push(`Ordered: ${lab.orderDate}`);
                if (lab.accessionNumber) parts.push(`Accession #: ${lab.accessionNumber}`);

                return parts.join(", ");
            })
            .join("; ");
    };

    // Build the HTML sections, only including sections that have data
    const vitalsHTML = buildVitalsHTML();
    const diagnosesHTML = patientSummary.diagnoses ? patientSummary.diagnoses.join(", ") : "";
    const medicationsHTML = buildMedicationsHTML();
    const labsHTML = buildLabsHTML();

    let heading = patientSummary.patientName + " (ID:" + patientSummary.patientID + ",Visit: " + patientSummary.latestVisitDate;

    let summaryText = vitalsHTML ? "~VITALS~ -" + vitalsHTML : "";

    summaryText += diagnosesHTML ? "~DIAGNOSIS~ -" + diagnosesHTML : "";

    summaryText += medicationsHTML ? "~MEDICATIONS~ -" + medicationsHTML : "";

    summaryText += labsHTML ? "~LAB~ -" + labsHTML : "";

    summaryText += patientSummary.nextAppointment ? "~Next Appointment~ -" + patientSummary.nextAppointment : "";

    return {
        heading: heading,
        summaryText: summaryText,
    };
}
